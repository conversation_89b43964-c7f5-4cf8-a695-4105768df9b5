//! Plotting functionality for the Quantum Search Engine
//!
//! This module provides visualization capabilities to compare the performance
//! of classical search vs. <PERSON><PERSON>'s algorithm simulation using the plotters crate.

use crate::quantum_search::{ClassicalSearchResult, GroverSearchResult};
use plotters::prelude::*;
use std::error::Error;

/// Creates a comparison plot showing query counts for classical vs. <PERSON><PERSON>'s search
///
/// # Arguments
///
/// * `classical_result` - Results from the classical search algorithm
/// * `grover_result` - Results from <PERSON><PERSON>'s algorithm simulation
/// * `item_list` - The search space that was used
/// * `target` - The target item that was searched for
///
/// # Returns
///
/// Returns the filename of the generated plot on success, or an error if plotting fails.
///
/// # Example
///
/// ```rust
/// let filename = create_comparison_plot(&classical_result, &grover_result, &items, "42")?;
/// println!("Plot saved to {}", filename);
/// ```
pub fn create_comparison_plot(
    classical_result: &ClassicalSearchResult,
    grover_result: &GroverSearchResult,
    item_list: &[String],
    target: &str,
) -> Result<String, Box<dyn Error>> {
    let filename = format!(
        "grover_vs_classical_N{}_{}.png",
        item_list.len(),
        target.replace('.', "_")
    );

    let filename_clone = filename.clone();
    let root = BitMapBackend::new(&filename_clone, (800, 600)).into_drawing_area();
    root.fill(&WHITE)?;

    let mut chart = ChartBuilder::on(&root)
        .caption(
            &format!("Search Query Comparison (N={}) for Target: {}", item_list.len(), target),
            ("sans-serif", 30),
        )
        .margin(20)
        .x_label_area_size(50)
        .y_label_area_size(60)
        .build_cartesian_2d(
            0f32..2f32,
            0u32..(classical_result.queries.max(grover_result.queries) + 5) as u32,
        )?;

    chart
        .configure_mesh()
        .x_desc("Algorithm")
        .y_desc("Number of Queries")
        .x_label_formatter(&|x| {
            match *x as i32 {
                0 => "Classical Search".to_string(),
                1 => "Grover's Simulation".to_string(),
                _ => "".to_string(),
            }
        })
        .draw()?;

    // Draw bars
    chart.draw_series(
        [
            (0.0f32, classical_result.queries as u32, BLUE),
            (1.0f32, grover_result.queries as u32, GREEN),
        ]
        .iter()
        .map(|(x, y, color)| Rectangle::new([(*x - 0.3, 0), (*x + 0.3, *y)], color.filled())),
    )?;

    // Add value labels on bars
    chart.draw_series(
        [
            (0.0f32, classical_result.queries as u32),
            (1.0f32, grover_result.queries as u32),
        ]
        .iter()
        .map(|(x, y)| {
            Text::new(format!("{}", y), (*x, *y + 1), ("sans-serif", 20))
        }),
    )?;

    // Add footer text
    root.titled(
        &format!(
            "Search Space: {} items. Grover Iterations: {}",
            item_list.len(),
            grover_result.iterations
        ),
        ("sans-serif", 15),
    )?;

    root.present()?;

    Ok(filename)
}
