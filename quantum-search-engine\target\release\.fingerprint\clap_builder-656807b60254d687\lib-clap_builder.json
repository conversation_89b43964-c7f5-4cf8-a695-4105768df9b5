{"rustc": 16591470773350601817, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 2781995403500503566, "path": 15693030740670771119, "deps": [[5820056977320921005, "anstream", false, 12857686839288745872], [9394696648929125047, "anstyle", false, 3061851023717840148], [11166530783118767604, "strsim", false, 7594006991714687788], [11649982696571033535, "clap_lex", false, 887595998636042439]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\clap_builder-656807b60254d687\\dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}