<!DOCTYPE html>
<html>
<head>
    <title>Security Assessment Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2, h3 { color: #2c3e50; }
        .vulnerability { margin: 10px 0; padding: 10px; border-left: 4px solid #e74c3c; background-color: #f9f9f9; }
        .info { margin: 10px 0; padding: 10px; border-left: 4px solid #3498db; background-color: #f9f9f9; }
        .success { margin: 10px 0; padding: 10px; border-left: 4px solid #2ecc71; background-color: #f9f9f9; }
        pre { background-color: #f1f1f1; padding: 10px; overflow: auto; }
    </style>
</head>
<body>
    <h1>Security Assessment Report</h1>
    <p>Generated on Mon Jun  2 01:30:01 EDT 2025</p>
    
    <h2>Executive Summary</h2>
    <div class="info">
        <p>This report presents the findings of an automated security assessment performed against a DVWA (Damn Vulnerable Web Application) instance.</p>
    </div>
    
    <h2>System Information</h2>
    <div class="info">
        <pre>Information not available</pre>
    </div>
    
    <h2>Open Ports and Services</h2>
    <div class="vulnerability">
        <pre>PORT   STATE SERVICE VERSION
80/tcp open  http    Apache httpd 2.4.25 ((Debian))
| http-title: Login :: Damn Vulnerable Web Application (DVWA) v1.10 *Develop...
|_Requested resource was login.php
| http-cookie-flags: 
|   /: 
|     PHPSESSID: 
|_      httponly flag not set
|_http-server-header: Apache/2.4.25 (Debian)
| http-robots.txt: 1 disallowed entry 
|_/

Service detection performed. Please report any incorrect results at https://nmap.org/submit/ .
Nmap done: 1 IP address (1 host up) scanned in 6.40 seconds</pre>
    </div>
    
    <h2>Web Vulnerabilities</h2>
    <div class="vulnerability">
        <h3>Nikto Scan Results</h3>
        <pre>- Nikto v2.1.5/2.1.5
+ Target Host: localhost
+ Target Port: 80
+ GET /: The anti-clickjacking X-Frame-Options header is not present.
+ GET /: Cookie PHPSESSID created without the httponly flag
+ GET /: Cookie security created without the httponly flag
+ GET /robots.txt: Server leaks inodes via ETags, header found with file /robots.txt, fields: 0x1a 0x5780ba3955700 
+ GET //: File/dir '/' in robots.txt returned a non-forbidden or redirect HTTP code (302)
+ GET /robots.txt: "robots.txt" contains 1 entry which should be manually viewed.
+ -3268: GET /config/: /config/: Directory indexing found.
+ GET /config/: /config/: Configuration information may be available remotely.
+ -3268: GET /docs/: /docs/: Directory indexing found.
+ -3233: GET /icons/README: /icons/README: Apache default file found.
+ GET /login.php: /login.php: Admin login page/section found.</pre>
        
        <h3>Directory Enumeration</h3>
        <pre>
-----------------
DIRB v2.22    
By The Dark Raver
-----------------

OUTPUT_FILE: scan_results_20250602_012939/dirb_results.txt
START_TIME: Mon Jun  2 01:29:50 2025
URL_BASE: http://localhost/
WORDLIST_FILES: /usr/share/dirb/wordlists/common.txt

-----------------

GENERATED WORDS: 4612

---- Scanning URL: http://localhost/ ----
==> DIRECTORY: http://localhost/config/
==> DIRECTORY: http://localhost/docs/
==> DIRECTORY: http://localhost/external/
+ http://localhost/favicon.ico (CODE:200|SIZE:1406)</pre>
    </div>
    
    <h2>Exploitation Results</h2>
    <div class="vulnerability">
        <h3>SQL Injection</h3>
        <pre>No successful exploitation</pre>
        
        <h3>Command Injection</h3>
        <pre>No successful exploitation</pre>
        
        <h3>File Inclusion</h3>
        <pre>No successful exploitation</pre>
    </div>
    
    <h2>Recommendations</h2>
    <div class="info">
        <ul>
            <li>Update web server software to the latest version</li>
            <li>Implement proper input validation for all user inputs</li>
            <li>Use prepared statements for database queries</li>
            <li>Implement proper access controls</li>
            <li>Enable security headers (Content-Security-Policy, X-XSS-Protection, etc.)</li>
        </ul>
    </div>
</body>
</html>
