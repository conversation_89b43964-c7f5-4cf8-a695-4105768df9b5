{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"async\", \"default\", \"futures\", \"std\"]", "target": 8369499057004385739, "profile": 16503403049695105087, "path": 8721008045276105974, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\weezl-c170016a9beb8f76\\dep-lib-weezl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}