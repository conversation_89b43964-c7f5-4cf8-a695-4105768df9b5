# PenTesting Scripts and Tools

This repository contains various scripts and tools for penetration testing purposes.

## Quantum Tools

This section includes tools that simulate or interact with quantum computing concepts relevant to security.

### `quantum_tools/quantum_grover_sim.py`

**Purpose**: Simulates <PERSON><PERSON>'s quantum search algorithm and compares its query count against a classical brute-force search on a small, user-defined keyspace. This script is intended as an educational tool to illustrate the theoretical O(sqrt(N)) speedup of <PERSON><PERSON>'s algorithm.

**Algorithm Details**:
*   **Classical Search**: Performs a simple linear iteration through the item list, comparing each item to the target. Queries are counted as direct comparisons.
*   **<PERSON>r's Simulation**:
    *   This is a pure Python simulation and does not require actual quantum hardware or SDKs like Qiskit.
    *   It simulates quantum amplitudes for items in the list.
    *   Initial state: Uniform superposition of all items.
    *   Oracle: Marks the target item by flipping its amplitude's phase. Each oracle call counts as one "quantum query".
    *   Diffusion Operator (Amplitude Amplification): Inverts amplitudes about the mean.
    *   Iterations: The number of iterations (oracle call + diffusion) is calculated as approximately `floor( (pi/4) * sqrt(N) )`, where N is the size of the search space.
    *   Measurement: Simulated by selecting the item with the highest probability (amplitude squared) after the iterations. For tie-breaking (e.g. N=2 case), if the true target is among those with maximum probability, it is chosen.

**Usage**:
The script is run from the command line.

```bash
python quantum_tools/quantum_grover_sim.py --target <target_string> --range <start-end> [--matplotlib]
```

**Arguments**:
*   `--target <target_string>`: (Required) The string you are searching for. For example, a 4-digit PIN like `"1234"`.
*   `--range <start-end>`: (Required) Defines the numerical range for the search space. Items are generated as strings, zero-padded to match the length of the `target_string`. For example, `0-9999` for a 4-digit PIN search space.
*   `--matplotlib`: (Optional) If this flag is included and `matplotlib` is installed (`pip install matplotlib`), a bar chart comparing the query counts of classical search vs. Grover's simulation will be generated and saved as a PNG file in the current directory.

**Example**:
To search for the PIN "0532" in a space of 4-digit PINs from "0000" to "9999" and generate a plot:
```bash
python quantum_tools/quantum_grover_sim.py --target "0532" --range "0000-9999" --matplotlib
```

**Output**:
The script will print:
*   The target item and the size of the search space.
*   Results from the classical search (found status, item found, queries).
*   Results from Grover's simulation (measured item, found status, oracle queries, iterations).
*   If `--matplotlib` is used, a message indicating the plot filename.

**Dependencies**:
*   Python 3.x
*   `matplotlib` (optional, for plotting): Install via `pip install matplotlib`.
