import unittest
import sys
import os

# Add the parent directory (project root) to the Python path
# to allow importing from quantum_tools
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from quantum_tools.quantum_grover_sim import classical_search, grover_search_simulation, generate_item_list

class TestQuantumGroverSim(unittest.TestCase):

    def test_generate_item_list(self):
        self.assertEqual(generate_item_list("0-3", 1), ["0", "1", "2", "3"])
        self.assertEqual(generate_item_list("00-03", 2), ["00", "01", "02", "03"])
        self.assertEqual(generate_item_list("9-11", 2), ["09", "10", "11"])
        with self.assertRaisesRegex(ValueError, "Start of range cannot be greater than end"):
            generate_item_list("5-1", 1)
        with self.assertRaisesRegex(ValueError, "Invalid range format .* Expected 'start-end'"):
            generate_item_list("1_5", 1)
        with self.assertRaisesRegex(ValueError, "Invalid range values .* Both start and end must be integers"):
            generate_item_list("a-5", 1)

    def test_classical_search(self):
        items = ["a", "b", "c", "d"]
        # Target present
        result = classical_search("c", items)
        self.assertTrue(result['found'])
        self.assertEqual(result['index'], 2)
        self.assertEqual(result['queries'], 3)

        # Target not present
        result = classical_search("e", items)
        self.assertFalse(result['found'])
        self.assertEqual(result['index'], -1)
        self.assertEqual(result['queries'], 4)

        # Empty list
        result = classical_search("a", [])
        self.assertFalse(result['found'])
        self.assertEqual(result['index'], -1)
        self.assertEqual(result['queries'], 0)

        # List with one item, target present
        result = classical_search("a", ["a"])
        self.assertTrue(result['found'])
        self.assertEqual(result['index'], 0)
        self.assertEqual(result['queries'], 1)

        # List with one item, target not present
        result = classical_search("b", ["a"])
        self.assertFalse(result['found'])
        self.assertEqual(result['index'], -1)
        self.assertEqual(result['queries'], 1)

    def test_grover_search_simulation_small_n(self):
        # N=1
        items_n1 = ["42"]
        result_n1_found = grover_search_simulation("42", items_n1)
        self.assertTrue(result_n1_found['found'])
        self.assertEqual(result_n1_found['measured_index'], 0)
        # For N=1, if target is present, iterations should be 0 or 1, queries 0 or 1.
        # Current implementation: num_iterations = 0, queries = 0 if n=1 and target_idx != -1
        # Let's adjust the test to current logic for N=1: 0 iterations, 0 queries if target is the only item.
        # If the logic implies oracle is called if target is present: queries should be 1.
        # The code sets num_iterations = 0 for n=1. Let's assume this means 0 queries.
        # If target_idx != -1 and n == 1, num_iterations = 0. Loop for range(0) doesn't run. queries = 0.
        # This means it "finds" it without a query, which is fine for N=1.
        self.assertEqual(result_n1_found['queries'], 0)
        self.assertEqual(result_n1_found['iterations'], 0)

        result_n1_not_found_list = grover_search_simulation("43", items_n1) # Target not in list
        self.assertFalse(result_n1_not_found_list['found'])
        # For N=1 and target not in list, target_idx = -1. num_iterations = 0. queries = 0.
        self.assertEqual(result_n1_not_found_list['queries'], 0)
        self.assertEqual(result_n1_not_found_list['iterations'], 0)


        # N=2, target "b" (index 1)
        items_n2 = ["a", "b"]
        result_n2 = grover_search_simulation("b", items_n2)
        self.assertTrue(result_n2['found'])
        self.assertEqual(result_n2['measured_index'], 1)
        self.assertEqual(result_n2['queries'], 1) # Approx (pi/4)*sqrt(2) = 1.11 -> floor(1.11) = 1 iteration
        self.assertEqual(result_n2['iterations'], 1)

        # N=4, target "c" (index 2)
        # Optimal iterations for N=4, M=1 is 1. (pi/4)*sqrt(4) = pi/2 ~ 1.57. floor(1.57) = 1.
        items_n4 = ["a", "b", "c", "d"]
        result_n4 = grover_search_simulation("c", items_n4)
        self.assertTrue(result_n4['found'])
        self.assertEqual(result_n4['measured_index'], 2)
        self.assertEqual(result_n4['queries'], 1)
        self.assertEqual(result_n4['iterations'], 1)

        # N=8, target "g" (index 6)
        # (pi/4)*sqrt(8) ~ (0.785)*2.828 ~ 2.22. floor(2.22) = 2 iterations.
        items_n8 = [str(i) for i in range(8)] # "0" to "7"
        target_n8 = "6"
        result_n8 = grover_search_simulation(target_n8, items_n8)
        self.assertTrue(result_n8['found'])
        self.assertEqual(result_n8['measured_index'], items_n8.index(target_n8))
        self.assertEqual(result_n8['queries'], 2)
        self.assertEqual(result_n8['iterations'], 2)


    def test_grover_target_not_in_list(self):
        items = ["10", "20", "30", "40"]
        result = grover_search_simulation("50", items)
        self.assertFalse(result['found'])
        self.assertEqual(result['target_index'], -1)
        # Queries should still be based on N, even if target isn't there.
        # N=4, iterations = 1.
        self.assertEqual(result['queries'], 1)
        self.assertEqual(result['iterations'], 1)
        # measured_index could be anything, not strictly testable unless we seed random.

    def test_grover_empty_list(self):
        result = grover_search_simulation("a", [])
        self.assertFalse(result['found'])
        self.assertEqual(result['queries'], 0)
        self.assertEqual(result['iterations'], 0)
        self.assertEqual(result['measured_index'], -1)

    # It's hard to deterministically test the "measured_index" if the simulation
    # isn't perfect or if multiple items have high probability.
    # The 'found' flag (measured_idx == target_idx) is the most important for correctness here.
    # We mostly care that it *usually* finds it and query counts are as expected.

    # For larger N, the probability of finding the target is high but not 100% guaranteed
    # in a simple simulation without true quantum state vectors / complex numbers.
    # Our current simulation uses floats for amplitudes and might hit precision issues for large N,
    # but for small N, it should be quite deterministic.

if __name__ == '__main__':
    unittest.main()
