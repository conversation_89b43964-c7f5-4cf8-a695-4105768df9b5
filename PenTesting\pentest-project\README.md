# Penetration Testing Project

Automated security testing workflow using Docker containers and various security tools.

## Overview

This project provides a comprehensive framework for conducting automated security assessments against web applications. It uses DVWA (Damn Vulnerable Web Application) as a target for demonstration purposes.

## Tools Used

- Docker with DVWA (Damn Vulnerable Web Application)
- Nmap for network scanning
- <PERSON><PERSON> for web vulnerability scanning
- Dirb for directory brute forcing
- Custom scripts for exploitation testing
- Automated report generation

## Prerequisites

- Docker
- Nmap
- Nikto
- Dirb
- wkhtmltopdf (optional, for PDF report generation)

## Installation

```bash
# Install required tools on Ubuntu/Debian
sudo apt update
sudo apt install -y docker.io nmap nikto dirb wkhtmltopdf

# Make scripts executable
chmod +x scripts/*.sh
chmod +x run_all.sh
```

## Usage

### Run the complete workflow

```bash
./run_all.sh
```

### Run individual components

```bash
# Setup the environment
./scripts/setup.sh

# Run security scans
./scripts/scan.sh

# Run exploitation tests
./scripts/exploit.sh

# Generate security report
./scripts/report.sh

# Clean up the environment
./scripts/cleanup.sh
```

## Report

The security report is generated in both HTML and PDF formats in a timestamped directory. It includes:

- Executive summary
- System information
- Open ports and services
- Web vulnerabilities
- Exploitation results
- Security recommendations

## Disclaimer

This project is for educational purposes only. Only use these tools against systems you have permission to test.
