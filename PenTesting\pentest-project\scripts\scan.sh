#!/bin/bash
# Run comprehensive security scans against target

# Create output directory
OUTDIR="scan_results_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTDIR"

echo "[+] Running Nmap scan..."
nmap -Pn -A localhost -oX "$OUTDIR/nmap_results.xml" > "$OUTDIR/nmap_results.txt"

echo "[+] Running Nikto web vulnerability scanner..."
nikto -h localhost -output "$OUTDIR/nikto_results.txt" 2>/dev/null

echo "[+] Running directory brute force with dirb..."
dirb http://localhost /usr/share/dirb/wordlists/common.txt -o "$OUTDIR/dirb_results.txt" 2>/dev/null

echo "[+] Testing for SQL injection vulnerabilities..."
curl -s "http://localhost/vulnerabilities/sqli/?id=1%27%20OR%20%271%27=%271&Submit=Submit" > "$OUTDIR/sqli_test.html"

echo "[+] Testing for XSS vulnerabilities..."
curl -s "http://localhost/vulnerabilities/xss_r/?name=<script>alert(1)</script>" > "$OUTDIR/xss_test.html"

echo "[+] Checking for default credentials..."
curl -s -c "$OUTDIR/cookies.txt" http://localhost/login.php > /dev/null
curl -s -b "$OUTDIR/cookies.txt" -d "username=admin&password=password&Login=Login" -X POST http://localhost/login.php > "$OUTDIR/login_result.html"

echo "[+] All scans completed. Results saved to $OUTDIR/"
