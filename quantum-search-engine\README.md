# Quantum Search Engine (Rust Implementation)

A high-performance Rust implementation of quantum search algorithms, including <PERSON><PERSON>'s algorithm simulation. This project demonstrates the theoretical quantum speedup compared to classical search algorithms.

## Features

- **Classical Search**: Linear brute-force search implementation
- **<PERSON>r's Algorithm Simulation**: Pure Rust simulation of <PERSON><PERSON>'s quantum search algorithm
- **Performance Comparison**: Side-by-side comparison of query counts
- **Visualization**: Generate comparison plots using the plotters crate
- **Command-Line Interface**: Easy-to-use CLI with comprehensive options
- **Comprehensive Testing**: Full test suite ensuring correctness

## Algorithm Details

### Classical Search
Performs a simple linear iteration through the item list, comparing each item to the target. Queries are counted as direct comparisons.

### <PERSON><PERSON>'s Simulation
- **Pure Rust Implementation**: No quantum hardware or external quantum SDKs required
- **Quantum Amplitude Simulation**: Simulates quantum amplitudes for items in the search space
- **Initial State**: Uniform superposition of all items
- **Oracle**: Marks the target item by flipping its amplitude's phase (each oracle call counts as one "quantum query")
- **Diffusion Operator**: Inverts amplitudes about the mean (amplitude amplification)
- **Iterations**: Calculated as approximately `floor((π/4) * sqrt(N))`, where N is the size of the search space
- **Measurement**: Simulated by selecting the item with the highest probability (amplitude squared)

## Installation

### Prerequisites
- Rust 1.70+ (install from [rustup.rs](https://rustup.rs/))

### Building from Source
```bash
git clone <https://github.com/xingxerx/quantum-search-engine.git>
cd quantum-search-engine
cargo build --release
```

## Usage

### Basic Usage
```bash
cargo run -- --target <target_string> --range <start-end>
```

### With Plotting
```bash
cargo run -- --target <target_string> --range <start-end> --matplotlib
```

### Arguments
- `--target <target_string>`: (Required) The string you are searching for. For example, a 4-digit PIN like `"1234"`
- `--range <start-end>`: (Required) Defines the numerical range for the search space. Items are generated as strings, zero-padded to match the length of the target string. For example, `0-9999` for a 4-digit PIN search space
- `--matplotlib`: (Optional) Generate a bar chart comparing the query counts of classical search vs. Grover's simulation

### Examples

Search for PIN "0532" in a 4-digit PIN space:
```bash
cargo run -- --target "0532" --range "0000-9999"
```

Search with visualization:
```bash
cargo run -- --target "42" --range "0-99" --matplotlib
```

Small search space example:
```bash
cargo run -- --target "7" --range "0-15" --matplotlib
```

## Performance Comparison

The Rust implementation provides significant performance improvements over the Python version:

- **Memory Safety**: Zero-cost abstractions with compile-time memory safety
- **Performance**: Typically 10-100x faster than the Python implementation
- **Concurrency**: Built-in support for safe concurrent operations
- **Type Safety**: Compile-time error checking prevents runtime errors

## Output

The program outputs:
- Target item and search space size
- Classical search results (found status, item found, queries)
- Grover's simulation results (measured item, found status, oracle queries, iterations)
- Plot filename (if `--matplotlib` is used)

Example output:
```
Target: 0532
Search space size: 10000 items (from '0000' to '9999')

--- Classical Search ---
  Found: true
  Item Found: 0532 at index 532
  Queries: 533

--- Grover's Simulation ---
  Measured Item: 0532 (at index 532)
  Found (Target == Measured Item): true
  Oracle Queries: 78
  Iterations: 78

Plotting requested.
Plot saved to grover_vs_classical_N10000_0532.png
```

## Testing

Run the comprehensive test suite:
```bash
cargo test
```

Run tests with output:
```bash
cargo test -- --nocapture
```

## Dependencies

- `clap`: Command-line argument parsing
- `plotters`: High-performance plotting library
- `rand`: Random number generation for tie-breaking
- `thiserror`: Error handling

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## Quantum Computing Background

This simulation demonstrates the theoretical O(√N) speedup of Grover's algorithm compared to classical O(N) search. While this is a classical simulation and doesn't provide actual quantum speedup, it illustrates the algorithmic principles that would apply on real quantum hardware.

For more information about Grover's algorithm, see:
- [Grover's Algorithm on Wikipedia](https://en.wikipedia.org/wiki/Grover%27s_algorithm)
- [Quantum Computing: An Applied Approach](https://www.springer.com/gp/book/9783030239213)
