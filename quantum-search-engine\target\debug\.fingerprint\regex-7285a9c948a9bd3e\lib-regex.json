{"rustc": 16591470773350601817, "features": "[\"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 15657897354478470176, "path": 8951051242614557546, "deps": [[555019317135488525, "regex_automata", false, 3153621268987518326], [9408802513701742484, "regex_syntax", false, 13844007335003015051]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-7285a9c948a9bd3e\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}