#!/bin/bash
# Run the complete penetration testing workflow

echo "===== DVWA Penetration Testing Workflow ====="
echo

echo "Step 1: Setting up environment..."
./pentest-project/scripts/setup.sh
echo

echo "Step 2: Running security scans..."
./pentest-project/scripts/scan.sh
echo

echo "Step 3: Running exploitation tests..."
./pentest-project/scripts/exploit.sh
echo

echo "Step 4: Generating security report..."
./pentest-project/scripts/report.sh
echo

echo "Step 5: Cleaning up environment..."
./pentest-project/scripts/cleanup.sh
echo

echo "===== Penetration Testing Complete ====="
echo "Check the security_report_* directory for results."

