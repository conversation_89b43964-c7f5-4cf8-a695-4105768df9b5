# Placeholder for <PERSON><PERSON>'s algorithm simulation
# This file will contain the classical search, <PERSON><PERSON>'s simulation,
# CLI argument parsing, and main script logic.

def classical_search(target_item: str, item_list: list[str]) -> dict:
    """
    Performs a classical brute-force search for a target item in a list.

    Args:
        target_item: The item to search for.
        item_list: The list of items to search within.

    Returns:
        A dictionary containing:
            'found': True if the item is found, False otherwise.
            'queries': The number of comparisons made.
            'index': The index of the found item, or -1 if not found.
    """
    queries = 0
    for i, item in enumerate(item_list):
        queries += 1
        if item == target_item:
            return {'found': True, 'queries': queries, 'index': i}
    return {'found': False, 'queries': queries, 'index': -1}

# Grover search simulation function (already implemented in previous step)
# ... grover_search_simulation ...

def generate_item_list(range_str: str, target_len: int) -> list[str]:
    """
    Generates a list of strings based on a range string (e.g., "0000-9999").
    All items will be zero-padded to match the target_len.
    """
    try:
        start_str, end_str = range_str.split('-')
        start = int(start_str)
        end = int(end_str)
        if start > end:
            raise ValueError("Start of range cannot be greater than end.")

        return [str(i).zfill(target_len) for i in range(start, end + 1)]
    except ValueError as e:
        # More specific error for parsing int
        if isinstance(e, ValueError) and "invalid literal for int()" in str(e):
             raise ValueError(f"Invalid range values in '{range_str}'. Both start and end must be integers. Error: {e}")
        # More specific error for split
        if isinstance(e, ValueError) and "not enough values to unpack" in str(e):
            raise ValueError(f"Invalid range format '{range_str}'. Expected 'start-end'. Error: {e}")
        raise ValueError(f"Invalid range format '{range_str}'. Expected 'start-end' with integers. Error: {e}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Simulate Classical vs. Grover's Search Algorithm.",
        formatter_class=argparse.RawTextHelpFormatter # To allow newlines in help text
    )
    parser.add_argument(
        "--target",
        type=str,
        required=True,
        help="The item to search for (e.g., a 4-digit PIN like '1234')."
    )
    parser.add_argument(
        "--range",
        type=str,
        required=True,
        help="Defines the search space. Format 'start-end' (e.g., '0000-9999' for numbers).\n"
             "Items will be zero-padded to match the length of the target string.\n"
             "For non-numeric ranges, this will need future enhancement (e.g. --list a,b,c or --file path.txt)."
    )
    parser.add_argument(
        "--matplotlib",
        action='store_true',
        help="If specified, generate a simple plot comparing queries (requires matplotlib installed)."
    )

    args = parser.parse_args()

    print(f"Target: {args.target}")
    target_length = len(args.target)

    try:
        item_list = generate_item_list(args.range, target_length)
        if not item_list:
            print(f"Error: Generated item list is empty for range '{args.range}'. Please check range.")
            exit(1)
        print(f"Search space size: {len(item_list)} items (from '{item_list[0]}' to '{item_list[-1]}')")
    except ValueError as e:
        print(f"Error generating item list: {e}")
        exit(1)

    if not any(isinstance(item, str) for item in item_list):
        print("Error: Item list generation failed to produce strings. This is unexpected.")
        exit(1)
    if not all(len(item) == target_length for item in item_list):
        print(f"Warning: Not all items in the generated list have the same length as the target ('{args.target}', length {target_length}).")
        print(f"Example item: '{item_list[0]}' (length {len(item_list[0])}). This might lead to issues if padding is inconsistent.")
        # Potentially exit or try to re-pad, but for now, a warning.

    # --- Classical Search ---
    classical_result = classical_search(args.target, item_list)
    print("\n--- Classical Search ---")
    print(f"  Found: {classical_result['found']}")
    if classical_result['found']:
        print(f"  Item Found: {item_list[classical_result['index']]} at index {classical_result['index']}")
    else:
        print(f"  Item Not Found.")
    print(f"  Queries: {classical_result['queries']}")

    # --- Grover's Simulation ---
    grover_result = grover_search_simulation(args.target, item_list)
    print("\n--- Grover's Simulation ---")
    # print(f"  Target Actual Index: {grover_result['target_index']}") # Debugging info

    measured_item_str = "N/A (invalid index)"
    if grover_result['measured_index'] != -1 and grover_result['measured_index'] < len(item_list):
        measured_item_str = item_list[grover_result['measured_index']]

    print(f"  Measured Item: {measured_item_str} (at index {grover_result['measured_index']})")
    print(f"  Found (Target == Measured Item): {grover_result['found']}")
    print(f"  Oracle Queries: {grover_result['queries']}")
    print(f"  Iterations: {grover_result['iterations']}")

    if args.target not in item_list and grover_result['target_index'] == -1:
        print(f"  Note: Target '{args.target}' was not found in the generated item list.")
    elif grover_result['target_index'] != -1 and not grover_result['found']:
        print(f"  Note: Grover's algorithm did not definitively find the target in the simulation "
              f"(measured '{measured_item_str}', target was '{args.target}' at index {grover_result['target_index']}). "
              f"This can happen due to the probabilistic nature or if iterations weren't optimal for this specific N.")


    if args.matplotlib:
        print("\nMatplotlib plotting requested.")
        try:
            import matplotlib.pyplot as plt
            labels = ['Classical Search', "Grover's Simulation"]
            queries = [classical_result['queries'], grover_result['queries']]

            fig, ax = plt.subplots()
            bars = ax.bar(labels, queries, color=['blue', 'green'])
            ax.set_ylabel('Number of Queries')
            ax.set_title(f'Search Query Comparison (N={len(item_list)}) for Target: {args.target}')
            ax.bar_label(bars) # Add query counts on top of bars

            plt.figtext(0.5, 0.01, f"Search Space: {len(item_list)} items. Grover Iterations: {grover_result['iterations']}", ha="center", fontsize=9)
            plt.tight_layout(rect=[0, 0.05, 1, 1]) # Adjust layout to make space for figtext

            plot_filename = f"grover_vs_classical_N{len(item_list)}_{args.target.replace('.', '_')}.png"
            plt.savefig(plot_filename)
            print(f"Plot saved to {plot_filename}")
            # plt.show() # Optionally show plot interactively
        except ImportError:
            print("  Matplotlib not found. Please install it to use the plotting feature: pip install matplotlib")
        except Exception as e:
            print(f"  Error during plotting: {e}")

import argparse # Already imported above, but good for clarity on where it's used.
import math
import random # For tie-breaking in simulated measurement

def grover_search_simulation(target_item: str, item_list: list[str]) -> dict:
    """
    Performs a simulation of Grover's quantum search algorithm.

    Args:
        target_item: The item to search for.
        item_list: The list of items to search within.

    Returns:
        A dictionary containing:
            'found': True if the measured item matches the target, False otherwise.
            'queries': The number of oracle calls made.
            'measured_index': The index of the item with the highest probability after iterations.
            'iterations': The number of Grover iterations performed.
            'target_index': The actual index of the target item (-1 if not found).
    """
    n = len(item_list)
    if n == 0:
        return {'found': False, 'queries': 0, 'measured_index': -1, 'iterations': 0, 'target_index': -1}

    try:
        target_idx = item_list.index(target_item)
    except ValueError:
        # Target is not in the list, Grover's can't find it.
        # For simulation purposes, we can say it 'measures' a random state after some iterations.
        # The number of queries would still be based on N.
        # Optimal iterations for "not found" is a bit ill-defined in simple simulation,
        # but we run it as if it were there to count queries.
        target_idx = -1

    # 1. Initialize amplitudes to uniform superposition
    amplitudes = [1.0 / math.sqrt(n)] * n

    num_iterations = 0
    # Optimal number of iterations is approx (pi/4)*sqrt(N)
    # If target_idx is -1 (target not in list), Grover's won't work as intended to find it.
    # We'll run it anyway to simulate query count, but it won't "find" the item.
    if n > 0 : # Avoid issues with log(0) or sqrt(0) if item_list was empty, though handled above.
      # For a list with only 1 item, 0 iterations are needed.
      # Grover's iteration formula (pi/4)*sqrt(N) is for N >> 1.
      # For N=1, it should be 0 iterations. For N=2, ~1 iter. N=3, ~1 iter. N=4, ~1 iter.
      # A more precise formula for optimal iterations when M=1 (one target): floor( (pi / (4 * asin(1/sqrt(N)))) - 1/2 )
      # Simplified: (pi/4)*sqrt(N) is generally good enough for simulation.
      if n == 1:
          num_iterations = 0 # Or 1, depending on interpretation. If it's the target, 0 queries.
                           # Let's assume at least one oracle call if target is present.
      else:
          num_iterations = math.floor((math.pi / 4.0) * math.sqrt(n))
      # Grover's algorithm is probabilistic. For small N, optimal iterations might be 0 or 1.
      # Ensure at least 1 iteration if target is present and N > 1 for the oracle to be called.
      if target_idx != -1 and n > 1 and num_iterations == 0:
          num_iterations = 1
      # If target is not in list, it doesn't make sense to run 0 iterations for N > 1
      if target_idx == -1 and n > 1 and num_iterations == 0:
          num_iterations = 1


    queries = 0

    for _ in range(num_iterations):
        # 2. Oracle: Flips the phase of the target state's amplitude
        # This is one "query"
        if target_idx != -1: # Oracle only acts if target exists
            amplitudes[target_idx] *= -1
        queries += 1 # Oracle call counts as a query

        # 3. Diffusion Operator (Inversion about the mean)
        # Calculate the mean amplitude
        mean_amplitude = sum(amplitudes) / n

        # Reflect amplitudes about the mean
        for i in range(n):
            amplitudes[i] = 2 * mean_amplitude - amplitudes[i]

    # 4. Simulated Measurement: Find the index with the highest probability (amplitude squared)
    # Probabilities are proportional to amplitude squared
    probabilities = [amp**2 for amp in amplitudes]

    # Handle cases where probabilities might be slightly off due to float precision
    # or if target was not in the list (all probabilities might be similar)
    max_prob = -1.0
    measured_idx = -1

    # Find all indices with probability close to maximum (within a small tolerance)
    # This handles cases where multiple states have (near) max probability,
    # or if target_idx = -1, then all states might have similar low probability.
    if probabilities:
        max_prob_val = max(probabilities)
        # Get all indices that have this max probability (or very close to it)
        potential_indices = [i for i, p in enumerate(probabilities) if math.isclose(p, max_prob_val)]

        if target_idx != -1 and target_idx in potential_indices and math.isclose(probabilities[target_idx], max_prob_val):
            # If target is among those with max probability, prioritize it for deterministic testing.
            # This helps in cases like N=2 where probabilities might be equal.
            measured_idx = target_idx
        elif potential_indices:
            measured_idx = random.choice(potential_indices) # Pick one randomly if multiple
        else: # Should not happen if probabilities list is not empty and max_prob_val is valid
            measured_idx = 0 if not probabilities else probabilities.index(max(probabilities)) if probabilities else -1


    found = (measured_idx == target_idx) and (target_idx != -1)

    return {
        'found': found,
        'queries': queries,
        'measured_index': measured_idx,
        'iterations': num_iterations,
        'target_index': target_idx
    }
