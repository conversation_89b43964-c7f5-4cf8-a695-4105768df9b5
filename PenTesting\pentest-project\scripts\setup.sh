#!/bin/bash
# Setup script for penetration testing environment

echo "[+] Setting up penetration testing environment..."

# Check if container already exists and remove it
if [ "$(docker ps -a -q -f name=dvwa-target)" ]; then
    echo "[+] Removing existing DVWA container..."
    docker stop dvwa-target >/dev/null 2>&1
    docker rm dvwa-target >/dev/null 2>&1
fi

# Start DVWA container
echo "[+] Starting DVWA container..."
docker run -d -p 80:80 --name dvwa-target vulnerables/web-dvwa

# Wait for container to fully start
echo "[+] Waiting for services to start..."
sleep 10

echo "[+] DVWA container started. Access at http://localhost"
echo "[+] Default credentials: admin/password"


