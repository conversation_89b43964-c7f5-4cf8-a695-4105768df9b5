{"rustc": 16591470773350601817, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 2781995403500503566, "path": 15102792655553339975, "deps": [[4925398738524877221, "clap_derive", false, 13792707854562739500], [14814905555676593471, "clap_builder", false, 2792638831382083021]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\clap-8392fab9b4ea17de\\dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}