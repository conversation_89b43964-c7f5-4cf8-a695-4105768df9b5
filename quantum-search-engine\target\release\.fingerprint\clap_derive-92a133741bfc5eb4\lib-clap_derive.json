{"rustc": 16591470773350601817, "features": "[\"default\"]", "declared_features": "[\"debug\", \"default\", \"deprecated\", \"raw-deprecated\", \"unstable-markdown\", \"unstable-v5\"]", "target": 905583280159225126, "profile": 3344701623380386, "path": 17451897004475099842, "deps": [[3060637413840920116, "proc_macro2", false, 17427259546423107831], [4974441333307933176, "syn", false, 3732103962599599004], [13077543566650298139, "heck", false, 7024291452906321827], [17990358020177143287, "quote", false, 7361512858482113241]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\clap_derive-92a133741bfc5eb4\\dep-lib-clap_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}