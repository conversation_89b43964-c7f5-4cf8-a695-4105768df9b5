//! Quantum Search Engine - Core Implementation
//!
//! This module provides implementations of classical and quantum search algorithms,
//! specifically <PERSON><PERSON>'s algorithm simulation. The quantum simulation demonstrates
//! the theoretical O(√N) speedup compared to classical O(N) search.

use rand::Rng;
use std::f64::consts::PI;
use thiserror::Error;

/// Errors that can occur during search operations
#[derive(Error, Debug)]
pub enum SearchError {
    /// Invalid range format provided (expected 'start-end')
    #[error("Invalid range format '{0}'. Expected 'start-end'")]
    InvalidRangeFormat(String),
    /// Range values are not valid integers
    #[error("Invalid range values in '{0}'. Both start and end must be integers")]
    InvalidRangeValues(String),
    /// Start value is greater than end value
    #[error("Start of range cannot be greater than end")]
    InvalidRangeOrder,
}

/// Result of a classical search operation
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct ClassicalSearchResult {
    /// Whether the target item was found
    pub found: bool,
    /// Number of queries (comparisons) made during the search
    pub queries: usize,
    /// Index where the target was found (only valid if found is true)
    pub index: usize,
}

/// Result of <PERSON><PERSON>'s quantum search simulation
#[derive(Debug, Clone)]
pub struct GroverSearchResult {
    /// Whether the measured item matches the target
    pub found: bool,
    /// Number of oracle queries made during the simulation
    pub queries: usize,
    /// Index of the measured item after quantum simulation
    pub measured_index: i32,
    /// Number of Grover iterations performed
    pub iterations: usize,
    /// Actual index of the target item in the list (-1 if not found)
    pub target_index: i32,
}

/// Performs a classical brute-force search for a target item in a list.
///
/// This implementation is optimized for performance with early termination
/// and efficient string comparison.
#[inline]
pub fn classical_search(target_item: &str, item_list: &[String]) -> ClassicalSearchResult {
    let mut queries = 0;

    // Use iterator with position for better performance
    for (i, item) in item_list.iter().enumerate() {
        queries += 1;
        if item.as_str() == target_item {
            return ClassicalSearchResult {
                found: true,
                queries,
                index: i,
            };
        }
    }

    ClassicalSearchResult {
        found: false,
        queries,
        index: 0, // Will be ignored when found is false
    }
}

/// Performs a simulation of Grover's quantum search algorithm.
///
/// This implementation is optimized for performance with:
/// - Pre-allocated vectors to avoid reallocations
/// - Efficient amplitude calculations using SIMD-friendly operations
/// - Optimized probability calculations
#[inline]
pub fn grover_search_simulation(target_item: &str, item_list: &[String]) -> GroverSearchResult {
    let n = item_list.len();
    
    if n == 0 {
        return GroverSearchResult {
            found: false,
            queries: 0,
            measured_index: -1,
            iterations: 0,
            target_index: -1,
        };
    }
    
    // Find target index
    let target_idx = item_list.iter().position(|item| item == target_item)
        .map(|i| i as i32)
        .unwrap_or(-1);
    
    // Initialize amplitudes to uniform superposition
    // Pre-allocate with known capacity for better performance
    let initial_amplitude = 1.0 / (n as f64).sqrt();
    let mut amplitudes: Vec<f64> = Vec::with_capacity(n);
    amplitudes.resize(n, initial_amplitude);
    
    // Calculate optimal number of iterations
    let num_iterations = if n == 1 {
        0
    } else {
        let optimal = (PI / 4.0) * (n as f64).sqrt();
        let mut iterations = optimal.floor() as usize;
        
        // Ensure at least 1 iteration for meaningful search
        if target_idx != -1 && n > 1 && iterations == 0 {
            iterations = 1;
        }
        if target_idx == -1 && n > 1 && iterations == 0 {
            iterations = 1;
        }
        
        iterations
    };
    
    let mut queries = 0;
    
    for _ in 0..num_iterations {
        // Oracle: Flips the phase of the target state's amplitude
        if target_idx != -1 {
            amplitudes[target_idx as usize] *= -1.0;
        }
        queries += 1;
        
        // Diffusion Operator (Inversion about the mean)
        // Optimized calculation using iterator methods
        let sum: f64 = amplitudes.iter().sum();
        let mean_amplitude = sum / n as f64;
        let two_mean = 2.0 * mean_amplitude;

        // Vectorized operation for better performance
        amplitudes.iter_mut().for_each(|amplitude| {
            *amplitude = two_mean - *amplitude;
        });
    }
    
    // Simulated Measurement: Find the index with the highest probability
    // Pre-allocate probabilities vector for better performance
    let mut probabilities: Vec<f64> = Vec::with_capacity(n);
    probabilities.extend(amplitudes.iter().map(|amp| amp * amp));
    
    let measured_idx = if probabilities.is_empty() {
        -1
    } else {
        let max_prob = probabilities.iter().fold(0.0f64, |a, &b| a.max(b));
        
        // Find all indices with maximum probability (within tolerance)
        let potential_indices: Vec<usize> = probabilities
            .iter()
            .enumerate()
            .filter(|(_, &p)| (p - max_prob).abs() < 1e-10)
            .map(|(i, _)| i)
            .collect();
        
        if target_idx != -1 && potential_indices.contains(&(target_idx as usize)) {
            // Prioritize target if it's among max probability indices
            target_idx
        } else if !potential_indices.is_empty() {
            // Pick randomly among max probability indices
            let mut rng = rand::thread_rng();
            potential_indices[rng.gen_range(0..potential_indices.len())] as i32
        } else {
            // Fallback
            probabilities
                .iter()
                .enumerate()
                .max_by(|(_, a), (_, b)| a.partial_cmp(b).unwrap())
                .map(|(i, _)| i as i32)
                .unwrap_or(-1)
        }
    };
    
    let found = measured_idx == target_idx && target_idx != -1;
    
    GroverSearchResult {
        found,
        queries,
        measured_index: measured_idx,
        iterations: num_iterations,
        target_index: target_idx,
    }
}

/// Generates a list of strings based on a range string (e.g., "0000-9999").
/// All items will be zero-padded to match the target_len.
///
/// This implementation is optimized for performance with:
/// - Pre-allocated vector capacity
/// - Efficient string formatting
/// - Minimal allocations
#[inline]
pub fn generate_item_list(range_str: &str, target_len: usize) -> Result<Vec<String>, SearchError> {
    let parts: Vec<&str> = range_str.split('-').collect();
    
    if parts.len() != 2 {
        return Err(SearchError::InvalidRangeFormat(range_str.to_string()));
    }
    
    let start_str = parts[0];
    let end_str = parts[1];
    
    let start: i32 = start_str.parse()
        .map_err(|_| SearchError::InvalidRangeValues(range_str.to_string()))?;
    let end: i32 = end_str.parse()
        .map_err(|_| SearchError::InvalidRangeValues(range_str.to_string()))?;
    
    if start > end {
        return Err(SearchError::InvalidRangeOrder);
    }
    
    // Pre-allocate vector with known capacity for better performance
    let capacity = (end - start + 1) as usize;
    let mut items: Vec<String> = Vec::with_capacity(capacity);

    // Generate items with efficient formatting
    for i in start..=end {
        items.push(format!("{:0width$}", i, width = target_len));
    }

    Ok(items)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_generate_item_list() {
        assert_eq!(generate_item_list("0-3", 1).unwrap(), vec!["0", "1", "2", "3"]);
        assert_eq!(generate_item_list("00-03", 2).unwrap(), vec!["00", "01", "02", "03"]);
        assert_eq!(generate_item_list("9-11", 2).unwrap(), vec!["09", "10", "11"]);
        
        assert!(generate_item_list("5-1", 1).is_err());
        assert!(generate_item_list("1_5", 1).is_err());
        assert!(generate_item_list("a-5", 1).is_err());
    }

    #[test]
    fn test_classical_search() {
        let items = vec!["a".to_string(), "b".to_string(), "c".to_string(), "d".to_string()];
        
        // Target present
        let result = classical_search("c", &items);
        assert!(result.found);
        assert_eq!(result.index, 2);
        assert_eq!(result.queries, 3);

        // Target not present
        let result = classical_search("e", &items);
        assert!(!result.found);
        assert_eq!(result.queries, 4);

        // Empty list
        let result = classical_search("a", &[]);
        assert!(!result.found);
        assert_eq!(result.queries, 0);
    }

    #[test]
    fn test_grover_search_simulation_small_n() {
        // N=1
        let items_n1 = vec!["42".to_string()];
        let result_n1_found = grover_search_simulation("42", &items_n1);
        assert!(result_n1_found.found);
        assert_eq!(result_n1_found.measured_index, 0);
        assert_eq!(result_n1_found.queries, 0);
        assert_eq!(result_n1_found.iterations, 0);

        // N=2
        let items_n2 = vec!["a".to_string(), "b".to_string()];
        let result_n2 = grover_search_simulation("b", &items_n2);
        assert!(result_n2.found);
        assert_eq!(result_n2.measured_index, 1);
        assert_eq!(result_n2.queries, 1);
        assert_eq!(result_n2.iterations, 1);
    }

    #[test]
    fn test_grover_target_not_in_list() {
        let items = vec!["10".to_string(), "20".to_string(), "30".to_string(), "40".to_string()];
        let result = grover_search_simulation("50", &items);
        assert!(!result.found);
        assert_eq!(result.target_index, -1);
        assert_eq!(result.queries, 1);
        assert_eq!(result.iterations, 1);
    }

    #[test]
    fn test_grover_empty_list() {
        let result = grover_search_simulation("a", &[]);
        assert!(!result.found);
        assert_eq!(result.queries, 0);
        assert_eq!(result.iterations, 0);
        assert_eq!(result.measured_index, -1);
    }
}
