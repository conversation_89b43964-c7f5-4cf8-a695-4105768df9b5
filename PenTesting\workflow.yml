name: Security Scan
on: push
jobs:
  pentest:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v2
      - name: Setup DVWA Container
        run: |
          docker run -d -p 80:80 --name dvwa-target vulnerables/web-dvwa
          # Wait for container to fully start
          sleep 10
      - name: Run Nmap Scan
        run: nmap -Pn -A localhost
      - name: Run Web Vulnerability Scan
        run: |
          # Example web vulnerability scan against DVWA
          # Replace with actual scanning tool of your choice
          curl -s http://localhost/setup.php
      - name: Cleanup
        run: docker stop dvwa-target && docker rm dvwa-target
