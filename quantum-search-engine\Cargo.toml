[package]
name = "quantum_search_engine"
version = "0.1.0"
edition = "2021"
description = "A Rust implementation of quantum search algorithms including <PERSON><PERSON>'s algorithm simulation"
authors = ["Your Name <<EMAIL>>"]
license = "MIT"

[dependencies]
clap = { version = "4.0", features = ["derive"] }
plotters = "0.3"
rand = "0.8"
thiserror = "1.0"

[profile.release]
# Optimize for performance
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
# Faster compilation for development
opt-level = 0
debug = true
