use criterion::{black_box, criterion_group, criterion_main, Criterion};
use quantum_search_engine::quantum_search::{
    classical_search, grover_search_simulation, generate_item_list
};

fn benchmark_classical_search(c: &mut Criterion) {
    let item_list = generate_item_list("0000-9999", 4).unwrap();
    let target = "5000";
    
    c.bench_function("classical_search_10k", |b| {
        b.iter(|| classical_search(black_box(target), black_box(&item_list)))
    });
}

fn benchmark_grover_search(c: &mut Criterion) {
    let item_list = generate_item_list("0000-9999", 4).unwrap();
    let target = "5000";
    
    c.bench_function("grover_search_10k", |b| {
        b.iter(|| grover_search_simulation(black_box(target), black_box(&item_list)))
    });
}

fn benchmark_item_generation(c: &mut Criterion) {
    c.bench_function("generate_item_list_10k", |b| {
        b.iter(|| generate_item_list(black_box("0000-9999"), black_box(4)))
    });
}

fn benchmark_small_search_space(c: &mut Criterion) {
    let item_list = generate_item_list("00-99", 2).unwrap();
    let target = "50";
    
    c.bench_function("classical_search_100", |b| {
        b.iter(|| classical_search(black_box(target), black_box(&item_list)))
    });
    
    c.bench_function("grover_search_100", |b| {
        b.iter(|| grover_search_simulation(black_box(target), black_box(&item_list)))
    });
}

criterion_group!(
    benches,
    benchmark_classical_search,
    benchmark_grover_search,
    benchmark_item_generation,
    benchmark_small_search_space
);
criterion_main!(benches);
