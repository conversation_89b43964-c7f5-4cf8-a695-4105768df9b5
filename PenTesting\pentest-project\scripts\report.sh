#!/bin/bash
# Generate comprehensive security report

SCAN_DIR=$(ls -td scan_results_* | head -1)
EXPLOIT_DIR=$(ls -td exploit_results_* | head -1)
REPORT_DIR="security_report_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$REPORT_DIR"

echo "[+] Generating security report..."

# Create HTML report
cat > "$REPORT_DIR/report.html" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Security Assessment Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2, h3 { color: #2c3e50; }
        .vulnerability { margin: 10px 0; padding: 10px; border-left: 4px solid #e74c3c; background-color: #f9f9f9; }
        .info { margin: 10px 0; padding: 10px; border-left: 4px solid #3498db; background-color: #f9f9f9; }
        .success { margin: 10px 0; padding: 10px; border-left: 4px solid #2ecc71; background-color: #f9f9f9; }
        pre { background-color: #f1f1f1; padding: 10px; overflow: auto; }
    </style>
</head>
<body>
    <h1>Security Assessment Report</h1>
    <p>Generated on $(date)</p>
    
    <h2>Executive Summary</h2>
    <div class="info">
        <p>This report presents the findings of an automated security assessment performed against a DVWA (Damn Vulnerable Web Application) instance.</p>
    </div>
    
    <h2>System Information</h2>
    <div class="info">
        <pre>$(grep -A 10 "Operating system" "$SCAN_DIR/nmap_results.txt" 2>/dev/null || echo "Information not available")</pre>
    </div>
    
    <h2>Open Ports and Services</h2>
    <div class="vulnerability">
        <pre>$(grep -A 20 "PORT" "$SCAN_DIR/nmap_results.txt" 2>/dev/null || echo "Information not available")</pre>
    </div>
    
    <h2>Web Vulnerabilities</h2>
    <div class="vulnerability">
        <h3>Nikto Scan Results</h3>
        <pre>$(head -20 "$SCAN_DIR/nikto_results.txt" 2>/dev/null || echo "Information not available")</pre>
        
        <h3>Directory Enumeration</h3>
        <pre>$(head -20 "$SCAN_DIR/dirb_results.txt" 2>/dev/null || echo "Information not available")</pre>
    </div>
    
    <h2>Exploitation Results</h2>
    <div class="vulnerability">
        <h3>SQL Injection</h3>
        <pre>$(grep -A 5 "VERSION()" "$EXPLOIT_DIR/sqli_exploit.html" 2>/dev/null || echo "No successful exploitation")</pre>
        
        <h3>Command Injection</h3>
        <pre>$(grep -A 5 "uid=" "$EXPLOIT_DIR/cmd_exploit.html" 2>/dev/null || echo "No successful exploitation")</pre>
        
        <h3>File Inclusion</h3>
        <pre>$(grep -A 5 "root:" "$EXPLOIT_DIR/fi_exploit.html" 2>/dev/null || echo "No successful exploitation")</pre>
    </div>
    
    <h2>Recommendations</h2>
    <div class="info">
        <ul>
            <li>Update web server software to the latest version</li>
            <li>Implement proper input validation for all user inputs</li>
            <li>Use prepared statements for database queries</li>
            <li>Implement proper access controls</li>
            <li>Enable security headers (Content-Security-Policy, X-XSS-Protection, etc.)</li>
        </ul>
    </div>
</body>
</html>
EOF

echo "[+] Creating PDF report..."
if command -v wkhtmltopdf >/dev/null 2>&1; then
    wkhtmltopdf "$REPORT_DIR/report.html" "$REPORT_DIR/report.pdf" 2>/dev/null
else
    echo "[!] wkhtmltopdf not found. PDF report not generated."
fi

echo "[+] Security report generated in $REPORT_DIR/"