- Nikto v2.1.5/2.1.5
+ Target Host: localhost
+ Target Port: 80
+ GET /: The anti-clickjacking X-Frame-Options header is not present.
+ GET /: <PERSON><PERSON> PHPSESSID created without the httponly flag
+ GET /: <PERSON>ie security created without the httponly flag
+ GET /robots.txt: Server leaks inodes via ETags, header found with file /robots.txt, fields: 0x1a 0x5780ba3955700 
+ GET //: File/dir '/' in robots.txt returned a non-forbidden or redirect HTTP code (302)
+ GET /robots.txt: "robots.txt" contains 1 entry which should be manually viewed.
+ -3268: GET /config/: /config/: Directory indexing found.
+ GET /config/: /config/: Configuration information may be available remotely.
+ -3268: GET /docs/: /docs/: Directory indexing found.
+ -3233: GET /icons/README: /icons/README: Apache default file found.
+ GET /login.php: /login.php: Admin login page/section found.
