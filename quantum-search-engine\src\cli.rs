//! Command-line interface for the Quantum Search Engine
//!
//! This module defines the CLI arguments and parsing logic using the clap crate.

use clap::Parser;

/// Command-line arguments for the quantum search engine
#[derive(Parser)]
#[command(
    name = "quantum_search_engine",
    about = "Simulate Classical vs. <PERSON><PERSON>'s Search Algorithm",
    long_about = "A Rust implementation of quantum search algorithms including <PERSON><PERSON>'s algorithm simulation"
)]
pub struct Args {
    /// The item to search for (e.g., a 4-digit PIN like '1234')
    #[arg(long, help = "The item to search for (e.g., a 4-digit PIN like '1234')")]
    pub target: String,

    /// Defines the search space. Format 'start-end' (e.g., '0000-9999' for numbers).
    /// Items will be zero-padded to match the length of the target string.
    #[arg(long, help = "Defines the search space. Format 'start-end' (e.g., '0000-9999' for numbers).\nItems will be zero-padded to match the length of the target string.")]
    pub range: String,

    /// If specified, generate a simple plot comparing queries
    #[arg(long, help = "If specified, generate a simple plot comparing queries")]
    pub matplotlib: bool,
}
