pub mod quantum_search;
mod cli;
mod plotting;

use clap::Parser;
use cli::Args;

/// Main entry point for the quantum search engine
///
/// This function is optimized for performance with minimal allocations
/// and efficient string operations.
fn main() -> Result<(), Box<dyn std::error::Error>> {
    let args = Args::parse();

    println!("Target: {}", args.target);
    let target_length = args.target.len();

    // Generate item list
    let item_list = quantum_search::generate_item_list(&args.range, target_length)?;

    if item_list.is_empty() {
        eprintln!("Error: Generated item list is empty for range '{}'. Please check range.", args.range);
        std::process::exit(1);
    }

    println!("Search space size: {} items (from '{}' to '{}')",
             item_list.len(), item_list[0], item_list[item_list.len() - 1]);

    // Classical Search
    let classical_result = quantum_search::classical_search(&args.target, &item_list);
    println!("\n--- Classical Search ---");
    println!("  Found: {}", classical_result.found);
    if classical_result.found {
        println!("  Item Found: {} at index {}",
                 item_list[classical_result.index], classical_result.index);
    } else {
        println!("  Item Not Found.");
    }
    println!("  Queries: {}", classical_result.queries);

    // Grover's Simulation
    let grover_result = quantum_search::grover_search_simulation(&args.target, &item_list);
    println!("\n--- Grover's Simulation ---");

    let measured_item_str = if grover_result.measured_index != -1 &&
                              (grover_result.measured_index as usize) < item_list.len() {
        &item_list[grover_result.measured_index as usize]
    } else {
        "N/A (invalid index)"
    };

    println!("  Measured Item: {} (at index {})", measured_item_str, grover_result.measured_index);
    println!("  Found (Target == Measured Item): {}", grover_result.found);
    println!("  Oracle Queries: {}", grover_result.queries);
    println!("  Iterations: {}", grover_result.iterations);

    if !item_list.contains(&args.target) && grover_result.target_index == -1 {
        println!("  Note: Target '{}' was not found in the generated item list.", args.target);
    } else if grover_result.target_index != -1 && !grover_result.found {
        println!("  Note: Grover's algorithm did not definitively find the target in the simulation \
                  (measured '{}', target was '{}' at index {}). \
                  This can happen due to the probabilistic nature or if iterations weren't optimal for this specific N.",
                 measured_item_str, args.target, grover_result.target_index);
    }

    // Plotting
    if args.matplotlib {
        println!("\nPlotting requested.");
        match plotting::create_comparison_plot(&classical_result, &grover_result, &item_list, &args.target) {
            Ok(filename) => println!("Plot saved to {}", filename),
            Err(e) => eprintln!("Error during plotting: {}", e),
        }
    }

    Ok(())
}
