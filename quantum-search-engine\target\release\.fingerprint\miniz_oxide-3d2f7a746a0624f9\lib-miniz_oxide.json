{"rustc": 16591470773350601817, "features": "[\"default\", \"simd\", \"simd-adler32\", \"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 11301701693415961412, "path": 11781783246754236012, "deps": [[4018467389006652250, "simd_adler32", false, 4566230990004973118], [7911289239703230891, "adler2", false, 4198575243752067865]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\miniz_oxide-3d2f7a746a0624f9\\dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}