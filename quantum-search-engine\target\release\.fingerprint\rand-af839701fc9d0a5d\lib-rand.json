{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 16503403049695105087, "path": 14109877255743401047, "deps": [[1573238666360410412, "rand_chacha", false, 9835063028227941664], [18130209639506977569, "rand_core", false, 3950473165107786350]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rand-af839701fc9d0a5d\\dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}